import torch.nn as nn
import pandas as pd
import torch
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from datetime import datetime
import logging
import numpy as np
import os
import pickle
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import MinMaxScaler
import math
import random
import warnings
from keras.models import Sequential
from keras.layers import Conv1D, Activation, Dropout, Dense, LSTM, Flatten, MaxPooling1D
import keras.optimizers
import pywt  # For wavelet transform

# Import custom modules
from iTansformerFFTlstm import iTransformerFFT
from run_lstmitransformer import train_model, evaluate_model

# Configuration
plt.rcParams["font.sans-serif"] = ["SimHei"]
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')
random.seed(88888)

# ===== CONFIGURABLE PARAMETERS =====
PRED_LENGTH = 1  # Configurable prediction length (default: 5 days)
LOOKBACK_LEN = 10  # Lookback window length
TRAIN_SPLIT = 0.8  # Use 80% of data for training

# Model parameters
num_variates = 1  # Wavelet components are single-variate
num_tokens_per_variate = 3
depth = 6
dim = 256
dim_head = 32
heads = 8
lstm_hidden_dim = 128
lr = 0.001
epochs = 1  # Set to 1 for faster training
SPLIT = 0.8  # Train/test split ratio (same as original)

# Wavelet parameters
WAVELET_NAME = 'db4'  # Daubechies 4 wavelet
DECOMP_LEVEL = None  # Auto-determine decomposition level

# Paths
DATA_PATH = os.path.normpath(os.path.join(os.path.dirname(__file__), '..', 'Dataset', 'tianranqilast.csv'))
MODEL_SAVE_DIR = "./wavemodel/"

print(f"=== Wavelet Training Configuration ===")
print(f"Prediction Length: {PRED_LENGTH} days")
print(f"Lookback Length: {LOOKBACK_LEN} days")
print(f"Training Split: {TRAIN_SPLIT}")
print(f"Wavelet: {WAVELET_NAME}")
print(f"Model Save Directory: {MODEL_SAVE_DIR}")
print("=" * 40)

# Create directories
def ensure_directories():
    """Create necessary directories if they don't exist"""
    os.makedirs('./figure/wavelet', exist_ok=True)
    os.makedirs(MODEL_SAVE_DIR, exist_ok=True)
    print(f"Ensured directories exist: ./figure/wavelet, {MODEL_SAVE_DIR}")

ensure_directories()

# Helper functions
def mape(y_true, y_pred):
    return np.mean(np.abs((y_pred - y_true) / y_true))

def error(y_true, y_pred):
    return (y_pred - y_true) / y_true

def directional_accuracy(y_true, y_pred):
    """Calculate the percentage of correct directional predictions"""
    if len(y_true) < 2 or len(y_pred) < 2:
        return 0.0

    actual_directions = np.diff(y_true) > 0
    predicted_directions = np.diff(y_pred) > 0
    correct_predictions = actual_directions == predicted_directions
    accuracy = np.mean(correct_predictions) * 100

    return accuracy

# Data loading and preprocessing
def load_and_prepare_data():
    """Load and prepare data for training"""
    print("Loading data...")
    data = pd.read_csv(DATA_PATH)
    data =  data[['Date','Close']]

    print("Original data shape:", data.shape)
    print("Columns:", data.columns.tolist())

    # Use only the Close column for wavelet analysis
    close_data = data['Close'].copy()

    print("Close data shape:", close_data.shape)

    # Calculate training size and split data
    train_size = int(len(data) * TRAIN_SPLIT)
    print(f"Training data size: {train_size} samples")
    print(f"Total data size: {len(data)} samples")

    # Use only training portion for training (no validation split during training)
    train_close_data = close_data[:train_size].copy()

    return train_close_data

# Wavelet decomposition functions
def get_wavelet_coeffs(data: pd.Series, wavelet=WAVELET_NAME, level=DECOMP_LEVEL):
    """
    Performs wavelet decomposition on the time series.

    :param data: Pandas Series containing the time series data.
    :param wavelet: Name of the wavelet to use (e.g., 'db4', 'haar').
    :param level: Decomposition level. If None, it's automatically determined.
    :returns: List of wavelet coefficients (approximation and details).
    """
    data_np = data.to_numpy()

    # Auto-determine decomposition level if not specified
    if level is None:
        level = pywt.dwt_max_level(len(data_np), pywt.Wavelet(wavelet).dec_len)

    coeffs = pywt.wavedec(data_np, wavelet=wavelet, level=level)

    print(f"Wavelet decomposition completed. Generated {len(coeffs)} components (1 approximation + {len(coeffs)-1} details).")

    # Create and save wavelet decomposition plot
    plot_wavelet_coeffs(data, coeffs, wavelet)

    return coeffs, level

def plot_wavelet_coeffs(data: pd.Series, coeffs, wavelet=WAVELET_NAME):
    """Plot wavelet decomposition components"""
    plt.figure(figsize=(18, 12))
    plt.subplot(len(coeffs) + 1, 1, 1)
    plt.plot(data, 'r')
    plt.ylabel("Close Price")
    plt.xlabel('Day')
    plt.title('Original Signal')

    for i, coeff_set in enumerate(coeffs):
        # Reconstruct each component individually for plotting
        temp_coeffs = [np.zeros_like(c) for c in coeffs]
        temp_coeffs[i] = coeff_set
        reconstructed_component = pywt.waverec(temp_coeffs, wavelet)

        plt.subplot(len(coeffs) + 1, 1, i + 2)
        # Ensure the reconstructed component has the same length as the original data
        min_len = min(len(data), len(reconstructed_component))
        plt.plot(data.index[:min_len], reconstructed_component[:min_len], 'g')
        if i == 0:
            plt.ylabel(f"Approximation (A{len(coeffs)-1})")
        else:
            plt.ylabel(f"Detail (D{len(coeffs)-i})")
        plt.locator_params(axis='y', nbins=4)

    plt.tight_layout()
    plt.savefig('./figure/wavelet/wavelet_decomposition_train.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Wavelet decomposition plot saved to ./figure/wavelet/wavelet_decomposition_train.png")

# Data preprocessing for configurable prediction length
def create_dataset_with_pred_length(dataset: np.ndarray, lookback_len, pred_length):
    """Create dataset with configurable prediction length"""
    dataX, dataY = [], []

    for i in range(len(dataset) - lookback_len - pred_length + 1):
        look_back_data = dataset[i:(i + lookback_len), 0]
        # For multi-step prediction, take the next pred_length values
        target_data = dataset[i + lookback_len:i + lookback_len + pred_length, 0]
        dataX.append(look_back_data)
        dataY.append(target_data)

    return np.array(dataX), np.array(dataY)

def preprocess_data_with_pred_length(data_component, lookback_len, pred_length):
    """
    Preprocess data by creating sequences with configurable prediction length

    Parameters:
    - data_component: Time series component data
    - lookback_len: Number of previous time steps to use for prediction
    - pred_length: Number of future time steps to predict

    Returns:
    - torch.Tensor: Tensor of sequences
    - torch.Tensor: Tensor of targets
    """
    sequences = []
    targets = []

    for i in range(len(data_component) - lookback_len - pred_length + 1):
        sequence = data_component[i:i + lookback_len]
        # Target is the next pred_length values
        target = data_component[i + lookback_len:i + lookback_len + pred_length]
        sequences.append(sequence)
        targets.append(target)

    return torch.tensor(np.array(sequences), dtype=torch.float32), torch.tensor(np.array(targets), dtype=torch.float32)

# Multi-step training function
def train_model_multistep(model, train_data, train_targets, epochs, lr, pred_length):
    """
    Train the model using Mean Squared Error loss and Adam optimizer for multi-step prediction.
    """
    optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    loss_fn = torch.nn.MSELoss()
    train_losses = []

    model.train()
    for epoch in range(epochs):
        predictions = model(train_data)

        # Check if predictions is a dictionary and extract the relevant output
        if isinstance(predictions, dict):
            first_key = list(predictions.keys())[0]
            predictions = predictions.get(first_key, None)
            if predictions is None:
                raise ValueError(f"Expected key '{first_key}' not found in model predictions")

        # Reshape targets to match predictions
        if len(train_targets.shape) == 3:
            targets_reshaped = train_targets.squeeze(-1)
        else:
            targets_reshaped = train_targets

        # Ensure predictions and targets have the same shape
        if len(predictions.shape) == 3:
            predictions = predictions[:, :, 0]  # Take the first feature

        loss = loss_fn(predictions, targets_reshaped)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        train_losses.append(loss.item())

        if epoch % 10 == 0:
            print(f'Epoch {epoch + 1}, Loss: {loss.item()}')

    return train_losses

# Model creation functions
def create_itransformer_model(lookback_len, pred_length):
    """Create iTransformerFFT model with configurable prediction length"""
    print(f"Creating iTransformer model with prediction length: {pred_length}")

    model = iTransformerFFT(
        num_variates=num_variates,
        lookback_len=lookback_len,
        num_tokens_per_variate=num_tokens_per_variate,
        depth=depth,
        dim=dim,
        pred_length=pred_length,
        dim_head=dim_head,
        heads=heads,
        use_reversible_instance_norm=True,
        lstm_hidden_dim=lstm_hidden_dim
    )

    return model

def create_lstm_model_with_pred_length(lookback_len, pred_length, layer_size=128):
    """Create LSTM-CNN model with configurable prediction length"""
    model = Sequential()
    model.add(LSTM(layer_size, input_shape=(1, lookback_len), return_sequences=True))
    model.add(Conv1D(filters=512, kernel_size=1, activation='relu', input_shape=(1, lookback_len)))
    model.add(MaxPooling1D(pool_size=1))
    model.add(Dropout(0.2))
    model.add(Activation('relu'))
    model.add(Flatten())
    model.add(Dense(pred_length))  # Output pred_length values

    try:
        adam = keras.optimizers.adam_v2.Adam(learning_rate=lr)
    except AttributeError:
        adam = keras.optimizers.Adam(learning_rate=lr)

    model.compile(loss='mse', optimizer=adam, metrics=['accuracy'])

    return model

# Training functions for wavelet components
def train_itransformer_component(component_data: np.ndarray, lookback_len, pred_length):
    """Train iTransformer model for wavelet component with configurable prediction length"""
    component_data = component_data.astype('float32')
    component_data_reshaped = np.reshape(component_data, (-1, 1))

    # Normalize the data
    scaler = MinMaxScaler()
    dataset_scaled = scaler.fit_transform(component_data_reshaped)

    # Split data into train/test like original implementation
    train_size = int(len(dataset_scaled) * SPLIT)
    train_data_scaled = dataset_scaled[:train_size, :]
    test_data_scaled = dataset_scaled[train_size:, :]

    # Prepare training data with configurable prediction length (use only training split)
    train_data, train_targets = preprocess_data_with_pred_length(train_data_scaled.flatten(), lookback_len, pred_length)

    # Ensure train_data is correctly shaped for iTransformerFFT: [batch, lookback_len, num_variates]
    if train_data.ndim == 2:  # If [batch, lookback_len]
        train_data = train_data.unsqueeze(-1)  # Add variate dimension

    print(f"iTransformer training data shape: {train_data.shape}")
    print(f"iTransformer training targets shape: {train_targets.shape}")

    # Create and train the model
    model = create_itransformer_model(lookback_len, pred_length)
    train_model_multistep(model=model, train_data=train_data, train_targets=train_targets,
                         epochs=epochs, lr=lr, pred_length=pred_length)

    return model, scaler, test_data_scaled

def train_lstm_component(component_data: np.ndarray, lookback_len, pred_length):
    """Train LSTM model for wavelet component with configurable prediction length"""
    component_data = component_data.astype('float32')
    component_data_reshaped = np.reshape(component_data, (-1, 1))

    # Normalize the data
    scaler = MinMaxScaler()
    dataset_scaled = scaler.fit_transform(component_data_reshaped)

    # Split data into train/test like original implementation
    train_size = int(len(dataset_scaled) * SPLIT)
    train_data_scaled = dataset_scaled[:train_size, :]
    test_data_scaled = dataset_scaled[train_size:, :]

    # Create datasets with configurable prediction length (use only training split)
    trainX, trainY = create_dataset_with_pred_length(train_data_scaled, lookback_len, pred_length)
    trainX = np.reshape(trainX, (trainX.shape[0], 1, trainX.shape[1]))

    print(f"LSTM training data: {len(trainX)}, prediction length: {pred_length}")

    # Create and train the LSTM model
    model = create_lstm_model_with_pred_length(lookback_len, pred_length, lstm_hidden_dim)
    model.fit(trainX, trainY, epochs=100, batch_size=100, verbose=1, validation_split=0.1)

    return model, scaler, test_data_scaled

def train_wavelet_models(wavelet_coeffs, lookback_len, pred_length, wavelet_name):
    """Train models for each wavelet component"""
    trained_models = {}
    trained_scalers = {}
    test_data_components = {}  # Store test data for each component

    for i, component_data in enumerate(wavelet_coeffs):
        print(f"\n=== Training Component {i + 1}/{len(wavelet_coeffs)} ===")

        # Reconstruct each component before training
        temp_coeffs_for_reconstruction = [np.zeros_like(c) for c in wavelet_coeffs]
        temp_coeffs_for_reconstruction[i] = component_data
        reconstructed_component = pywt.waverec(temp_coeffs_for_reconstruction, wavelet_name)

        if i == 0:
            # For the first component (approximation), use iTransformer
            print("Training iTransformer model for approximation component...")
            model, scaler, test_data = train_itransformer_component(reconstructed_component, lookback_len, pred_length)
            trained_models[f'itransformerfft_component_{i}'] = model
            trained_scalers[f'itransformerfft_component_{i}'] = scaler
            test_data_components[f'test_component_{i+1}'] = test_data
            
            # Save the iTransformer model using the new function
            save_model_and_scalers_wavelet(
                model=model, scaler=scaler, model_type='iTransformerFFT', 
                component_index=i, lookback_len=lookback_len, 
                wavelet_name=wavelet_name, decomp_level=None, epochs=epochs, lr=lr
            )
        else:
            # For detail components, use LSTM
            print(f"Training LSTM model for detail component {i}...")
            model, scaler, test_data = train_lstm_component(reconstructed_component, lookback_len, pred_length)
            trained_models[f'lstm_cnn_component_{i}'] = model
            trained_scalers[f'lstm_cnn_component_{i}'] = scaler
            test_data_components[f'test_component_{i+1}'] = test_data
            
            # Save the LSTM model using the new function
            save_model_and_scalers_wavelet(
                model=model, scaler=scaler, model_type='LSTM_CNN', 
                component_index=i, lookback_len=lookback_len, 
                wavelet_name=wavelet_name, decomp_level=None, epochs=epochs, lr=lr
            )

    return trained_models, trained_scalers, test_data_components

# Model saving functions
def save_wavelet_model_components(models_dict, scalers_dict, test_data_dict, config_dict, save_dir):
    """Save all wavelet model components, scalers, test data, and configuration"""
    print(f"Saving wavelet models to {save_dir}...")

    # Save configuration
    config_path = os.path.join(save_dir, 'wavelet_config.pkl')
    with open(config_path, 'wb') as f:
        pickle.dump(config_dict, f)
    print(f"Wavelet configuration saved to {config_path}")

    # Save scalers
    scalers_path = os.path.join(save_dir, 'wavelet_scalers.pkl')
    with open(scalers_path, 'wb') as f:
        pickle.dump(scalers_dict, f)
    print(f"Wavelet scalers saved to {scalers_path}")

    # Save test data components
    test_data_path = os.path.join(save_dir, 'wavelet_test_data.pkl')
    with open(test_data_path, 'wb') as f:
        pickle.dump(test_data_dict, f)
    print(f"Wavelet test data saved to {test_data_path}")

    # Save models
    for model_name, model in models_dict.items():
        if 'itransformer' in model_name.lower():
            # Save PyTorch model
            model_path = os.path.join(save_dir, f'{model_name}.pth')
            torch.save(model.state_dict(), model_path)
            print(f"PyTorch model {model_name} saved to {model_path}")
        else:
            # Save Keras model weights
            model_path = os.path.join(save_dir, f'{model_name}.weights.h5')
            model.save_weights(model_path)
            print(f"Keras model weights {model_name} saved to {model_path}")

    print("All wavelet models and components saved successfully!")

def save_model_and_scalers_wavelet(model, scaler, model_type, component_index, lookback_len, wavelet_name, decomp_level, epochs, lr):
    """
    Save trained model and scalers to the wavemodel directory
    
    Parameters:
    - model: Trained model (PyTorch or Keras)
    - scaler: Data scaler
    - model_type: Type of model ('iTransformerFFT' or 'LSTM_CNN')
    - component_index: Index of the wavelet component
    - lookback_len: Lookback length used for training
    - wavelet_name: Name of the wavelet used
    - decomp_level: Decomposition level
    - epochs: Number of training epochs
    - lr: Learning rate used
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save main scaler
    scaler_path = f'./wavemodel/{model_type.lower()}_component_{component_index}_scaler.pkl'
    with open(scaler_path, 'wb') as f:
        pickle.dump(scaler, f)
    print(f"Saved scaler to {scaler_path}")
    
    # Save model based on type
    try:
        if model_type == 'iTransformerFFT':
            model_path = f'./wavemodel/{model_type.lower()}_component_{component_index}.pth'
            torch.save(model.state_dict(), model_path)
            print(f"Saved iTransformerFFT model to {model_path}")
        elif model_type == 'LSTM_CNN':
            model_path = f'./wavemodel/{model_type.lower()}_component_{component_index}.weights.h5'
            # 确保模型使用标准配置，避免序列化问题
            model.save(model_path, save_format='h5')
            print(f"Saved LSTM_CNN model to {model_path}")
        else:
            raise ValueError(f"Unsupported model type: {model_type}")
    except Exception as e:
        print(f"Error saving model: {e}")
        print("Attempting to save model with custom_objects...")
        try:
            if model_type == 'LSTM_CNN':
                model_path = f'./wavemodel/{model_type.lower()}_component_{component_index}.weights.h5'
                # 使用custom_objects来避免序列化问题
                model.save(model_path, save_format='h5', include_optimizer=False)
                print(f"Saved LSTM_CNN model to {model_path} (without optimizer)")
            else:
                raise e
        except Exception as e2:
            print(f"Failed to save model: {e2}")
            raise e2
    
    # Save configuration
    config = {
        'model_type': model_type,
        'component_index': component_index,
        'lookback_len': lookback_len,
        'wavelet_name': wavelet_name,
        'decomp_level': decomp_level,
        'epochs': epochs,
        'learning_rate': lr,
        'timestamp': timestamp
    }
    config_path = f'./wavemodel/{model_type.lower()}_config_{component_index}.pkl'
    with open(config_path, 'wb') as f:
        pickle.dump(config, f)
    print(f"Saved configuration to {config_path}")

def run_wavelet_training_pipeline():
    """
    Main wavelet training pipeline that processes data, trains models, and saves everything
    """
    print("=== Starting Wavelet Training Pipeline ===")

    # Load and prepare data
    train_close_data = load_and_prepare_data()

    # Apply wavelet decomposition
    print("Applying wavelet decomposition to close price data...")
    wavelet_coeffs, decomp_level = get_wavelet_coeffs(train_close_data, WAVELET_NAME, DECOMP_LEVEL)

    # Train models for each wavelet component
    print("Training models for each wavelet component...")
    trained_models, trained_scalers, test_data_components = train_wavelet_models(
        wavelet_coeffs, LOOKBACK_LEN, PRED_LENGTH, WAVELET_NAME
    )
    
    # Update decomp_level in the trained models for saving
    for i, component_data in enumerate(wavelet_coeffs):
        if i == 0:
            # Update iTransformer model config with decomp_level
            save_model_and_scalers_wavelet(
                model=trained_models[f'itransformerfft_component_{i}'], 
                scaler=trained_scalers[f'itransformerfft_component_{i}'], 
                model_type='iTransformerFFT', 
                component_index=i, lookback_len=LOOKBACK_LEN, 
                wavelet_name=WAVELET_NAME, decomp_level=decomp_level, epochs=epochs, lr=lr
            )
        else:
            # Update LSTM model config with decomp_level
            save_model_and_scalers_wavelet(
                model=trained_models[f'lstm_cnn_component_{i}'], 
                scaler=trained_scalers[f'lstm_cnn_component_{i}'], 
                model_type='LSTM_CNN', 
                component_index=i, lookback_len=LOOKBACK_LEN, 
                wavelet_name=WAVELET_NAME, decomp_level=decomp_level, epochs=epochs, lr=lr
            )

    # Prepare configuration dictionary
    config = {
        'pred_length': PRED_LENGTH,
        'lookback_len': LOOKBACK_LEN,
        'train_split': SPLIT,
        'n_components': len(wavelet_coeffs),
        'wavelet_name': WAVELET_NAME,
        'decomp_level': decomp_level,
        'num_variates': num_variates,
        'num_tokens_per_variate': num_tokens_per_variate,
        'depth': depth,
        'dim': dim,
        'dim_head': dim_head,
        'heads': heads,
        'lstm_hidden_dim': lstm_hidden_dim,
        'lr': lr,
        'epochs': epochs,
        'data_path': DATA_PATH
    }

    # Save all models and components
    save_wavelet_model_components(trained_models, trained_scalers, test_data_components, config, MODEL_SAVE_DIR)

    print("\n=== Wavelet Training Pipeline Completed Successfully ===")
    print(f"Models saved to: {MODEL_SAVE_DIR}")
    print(f"Configuration: Prediction Length = {PRED_LENGTH}, Lookback Length = {LOOKBACK_LEN}")
    print(f"Wavelet: {WAVELET_NAME}, Decomposition Level = {decomp_level}")
    print(f"Total components trained: {len(wavelet_coeffs)}")

if __name__ == "__main__":
    try:
        run_wavelet_training_pipeline()
    except Exception as e:
        print(f"Error during wavelet training: {e}")
        import traceback
        traceback.print_exc()