import torch.nn as nn
import pandas as pd
import torch
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
from datetime import datetime
import logging
import numpy as np
import os
import pickle
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import MinMaxScaler
import math
import random
import warnings
from keras.models import Sequential, load_model
from keras.layers import Conv1D, Activation, Dropout, Dense, LSTM, Flatten, MaxPooling1D
import keras.optimizers
import pywt  # For wavelet transform

# Import custom modules
from iTansformerFFTlstm import iTransformerFFT
from run_lstmitransformer import train_model, evaluate_model

# Configuration
plt.rcParams["font.sans-serif"] = ["SimHei"]
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')
random.seed(88888)

# Paths
MODEL_LOAD_DIR = "./wavemodel"
# Make DATA_PATH robust regardless of current working directory
DATA_PATH = os.path.normpath(os.path.join(os.path.dirname(__file__), '..', 'Dataset', 'tianranqilast.csv'))

print("=== Wavelet Testing Configuration ===")
print(f"Model Load Directory: {MODEL_LOAD_DIR}")
print("=" * 40)

# Create directories
def ensure_directories():
    """Create necessary directories if they don't exist"""
    os.makedirs('./figure/wavelet', exist_ok=True)
    print("Ensured directories exist: ./figure/wavelet")

ensure_directories()

# Helper functions
def mape(y_true, y_pred):
    return np.mean(np.abs((y_pred - y_true) / y_true))

def error(y_true, y_pred):
    return (y_pred - y_true) / y_true

def directional_accuracy(y_true, y_pred):
    """Calculate the percentage of correct directional predictions"""
    if len(y_true) < 2 or len(y_pred) < 2:
        return 0.0

    actual_directions = np.diff(y_true) > 0
    predicted_directions = np.diff(y_pred) > 0
    correct_predictions = actual_directions == predicted_directions
    accuracy = np.mean(correct_predictions) * 100

    return accuracy

def calculate_trend_accuracy(actual, predicted):
    """
    Calculate trend accuracy - correctness of up/down predictions

    Parameters:
    - actual: Array of actual values
    - predicted: Array of predicted values

    Returns:
    - trend_accuracy: Percentage of correct trend predictions
    """
    if len(actual) < 2 or len(predicted) < 2:
        return 0.0

    correct_trends = 0
    total_trends = len(actual) - 1

    for i in range(1, len(actual)):
        # Determine actual trend (up/down)
        actual_trend = 1 if actual[i] > actual[i-1] else 0
        # Determine predicted trend (up/down) - compare predicted with previous actual
        predicted_trend = 1 if predicted[i] > actual[i-1] else 0

        if actual_trend == predicted_trend:
            correct_trends += 1

    trend_accuracy = (correct_trends / total_trends) * 100
    return trend_accuracy

# Model loading functions
def load_wavelet_model_components(load_dir):
    """
    Load available wavelet model components, scalers, and configuration from ./wavemodel/.

    It supports files saved by wavelet_ffttransformerlstm(1) copy.py, e.g.:
    - itransformerfft_component_0.pth
    - itransformerfft_component_0_scaler.pkl
    - itransformerfft_config_0.pkl
    - lstm_cnn_component_{k}.weights.h5
    - lstm_cnn_component_{k}_scaler.pkl
    - lstm_cnn_config_{k}.pkl

    Returns:
    - models: dict of loaded models keyed by component identifiers
      Keys used:
        'itransformerfft_component_0' for component 0
        'lstm_cnn_component_{k}' for components k >= 1
    - scalers: dict of scalers with the same keys as models
    - config: dict with testing configuration (merged from saved configs + sensible defaults)
    """
    print(f"Loading wavelet models from {load_dir}...")

    if not os.path.isdir(load_dir):
        raise FileNotFoundError(f"Model directory not found: {load_dir}")

    all_files = os.listdir(load_dir)

    # Defaults aligned with training script
    default_cfg = {
        'num_variates': 1,
        'num_tokens_per_variate': 3,
        'depth': 6,
        'dim': 256,
        'pred_length': 1,
        'dim_head': 32,
        'heads': 8,
        'lstm_hidden_dim': 128,
        'train_split': 0.8,
    }

    # Try to infer global settings from the saved config(s)
    inferred_cfg = {}

    def try_load_pickle(path):
        try:
            with open(path, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            print(f"Warning: failed to load pickle {path}: {e}")
            return None

    # Load iTransformerFFT component 0 if present
    models = {}
    scalers = {}

    if any(f.startswith('itransformerfft_component_0') and f.endswith('.pth') for f in all_files):
        comp_idx = 0
        pth_path = os.path.join(load_dir, f'itransformerfft_component_{comp_idx}.pth')
        scaler_path = os.path.join(load_dir, f'itransformerfft_component_{comp_idx}_scaler.pkl')
        cfg_path = os.path.join(load_dir, f'itransformerfft_config_{comp_idx}.pkl')

        saved_cfg = try_load_pickle(cfg_path)
        if isinstance(saved_cfg, dict):
            inferred_cfg.update(saved_cfg)

        # Build model
        itr_model = create_itransformer_model_for_loading({**default_cfg, **inferred_cfg})
        state = torch.load(pth_path, map_location='cpu')
        missing = itr_model.load_state_dict(state, strict=False)
        if missing.missing_keys:
            print(f"Warning: Missing keys when loading iTransformerFFT: {missing.missing_keys}")
        if missing.unexpected_keys:
            print(f"Warning: Unexpected keys when loading iTransformerFFT: {missing.unexpected_keys}")
        itr_model.eval()
        models['itransformerfft_component_0'] = itr_model
        print(f"Loaded PyTorch model itransformerfft_component_0 from {pth_path}")

        # Load scaler
        if os.path.exists(scaler_path):
            with open(scaler_path, 'rb') as f:
                scalers['itransformerfft_component_0'] = pickle.load(f)
            print(f"Loaded scaler for itransformerfft_component_0 from {scaler_path}")
        else:
            raise FileNotFoundError(f"Missing scaler file: {scaler_path}")

    # Load all LSTM_CNN components
    lstm_keys = sorted([
        f for f in all_files
        if f.startswith('lstm_cnn_component_') and f.endswith('.weights.h5')
    ])

    from keras.models import load_model as keras_load_model

    for weights_file in lstm_keys:
        # Extract component index
        try:
            comp_idx = int(weights_file.split('_')[3].split('.')[0])
        except Exception:
            # Fallback parsing: lstm_cnn_component_{k}.weights.h5
            comp_idx = int(weights_file.replace('lstm_cnn_component_', '').split('.')[0])

        weights_path = os.path.join(load_dir, weights_file)
        scaler_path = os.path.join(load_dir, f'lstm_cnn_component_{comp_idx}_scaler.pkl')
        cfg_path = os.path.join(load_dir, f'lstm_cnn_config_{comp_idx}.pkl')

        saved_cfg = try_load_pickle(cfg_path)
        if isinstance(saved_cfg, dict):
            # Keep latest seen values for shared fields
            for k in ['wavelet_name', 'decomp_level', 'lookback_len']:
                if k in saved_cfg:
                    inferred_cfg[k] = saved_cfg[k]

        # Load Keras model directly (full model was saved)
        lstm_model = keras_load_model(weights_path)
        models[f'lstm_cnn_component_{comp_idx}'] = lstm_model
        print(f"Loaded Keras model lstm_cnn_component_{comp_idx} from {weights_path}")

        # Load scaler
        if os.path.exists(scaler_path):
            with open(scaler_path, 'rb') as f:
                scalers[f'lstm_cnn_component_{comp_idx}'] = pickle.load(f)
            print(f"Loaded scaler for lstm_cnn_component_{comp_idx} from {scaler_path}")
        else:
            raise FileNotFoundError(f"Missing scaler file: {scaler_path}")

    if not models:
        raise RuntimeError(f"No models found in {load_dir}")

    # Compose final config used by testing
    final_cfg = {
        **default_cfg,
        **inferred_cfg
    }

    # Determine component count from loaded models
    loaded_component_indices = set()
    if 'itransformerfft_component_0' in models:
        loaded_component_indices.add(0)
    for key in models.keys():
        if key.startswith('lstm_cnn_component_'):
            try:
                loaded_component_indices.add(int(key.split('_')[-1]))
            except Exception:
                pass

    if loaded_component_indices:
        final_cfg['n_components'] = len(loaded_component_indices)
    else:
        final_cfg['n_components'] = 0

    # Ensure essential keys exist
    final_cfg.setdefault('lookback_len', final_cfg.get('lookback', 10))
    final_cfg.setdefault('wavelet_name', 'db4')
    final_cfg.setdefault('decomp_level', None)

    print("All wavelet models and components loaded successfully!")
    return models, scalers, None, final_cfg

def create_itransformer_model_for_loading(config):
    """Recreate iTransformer model architecture for loading (with safe defaults)."""
    model = iTransformerFFT(
        num_variates=config.get('num_variates', 1),
        lookback_len=config.get('lookback_len', 10),
        num_tokens_per_variate=config.get('num_tokens_per_variate', 3),
        depth=config.get('depth', 6),
        dim=config.get('dim', 256),
        pred_length=config.get('pred_length', 1),
        dim_head=config.get('dim_head', 32),
        heads=config.get('heads', 8),
        use_reversible_instance_norm=True,
        lstm_hidden_dim=config.get('lstm_hidden_dim', 128)
    )
    return model

def create_lstm_model_for_loading(config):
    """Kept for compatibility but unused (we load full Keras models directly)."""
    pred_length = config.get('pred_length', 1)
    lookback_len = config.get('lookback_len', 10)
    lstm_hidden_dim = config.get('lstm_hidden_dim', 128)
    lr = config.get('lr', 1e-3)

    model = Sequential()
    model.add(LSTM(lstm_hidden_dim, input_shape=(1, lookback_len), return_sequences=True))
    model.add(Conv1D(filters=512, kernel_size=1, activation='relu', input_shape=(1, lookback_len)))
    model.add(MaxPooling1D(pool_size=1))
    model.add(Dropout(0.2))
    model.add(Activation('relu'))
    model.add(Flatten())
    model.add(Dense(pred_length))

    try:
        adam = keras.optimizers.adam_v2.Adam(learning_rate=lr)
    except AttributeError:
        adam = keras.optimizers.Adam(learning_rate=lr)

    model.compile(loss='mse', optimizer=adam, metrics=['accuracy'])
    dummy_input = np.zeros((1, 1, lookback_len))
    model(dummy_input)
    return model

# Data loading and preprocessing for testing
def load_complete_data_and_extract_test_components(config):
    """
    Load complete dataset, perform wavelet decomposition, and extract test components
    This ensures wavelet components are consistent with training
    """
    print("Loading complete dataset for proper wavelet decomposition...")
    data = pd.read_csv(DATA_PATH)
    data =  data[['Date','Close']]

    print("Original data shape:", data.shape)

    # Use complete Close column for wavelet analysis
    complete_close_data = data['Close'].copy()

    print(f"Complete dataset size: {len(complete_close_data)} samples")
    print(f"Complete data range: [{complete_close_data.min():.4f}, {complete_close_data.max():.4f}]")

    # Perform wavelet decomposition on complete dataset
    print("Performing wavelet decomposition on complete dataset...")
    complete_wavelet_coeffs = get_wavelet_coeffs_test(
        complete_close_data, config['wavelet_name'], config['decomp_level']
    )

    print(f"Complete dataset decomposed into {len(complete_wavelet_coeffs)} components")

    # Extract test portion from each component (last 20%)
    train_size = int(len(complete_close_data) * config['train_split'])
    test_wavelet_components = {}

    for i, component_coeffs in enumerate(complete_wavelet_coeffs):
        # Reconstruct each component from complete dataset
        temp_coeffs_for_reconstruction = [np.zeros_like(c) for c in complete_wavelet_coeffs]
        temp_coeffs_for_reconstruction[i] = component_coeffs
        reconstructed_component = pywt.waverec(temp_coeffs_for_reconstruction, config['wavelet_name'])

        # Extract test portion (last 20%) from this component
        test_component = reconstructed_component[train_size:]
        test_wavelet_components[f'test_component_{i+1}'] = test_component.reshape(-1, 1)

        print(f"Component {i+1} test data shape: {test_component.shape}")

    return test_wavelet_components

def get_wavelet_coeffs_test(data: pd.Series, wavelet, level):
    """Apply wavelet decomposition to test data"""
    data_np = data.to_numpy()
    coeffs = pywt.wavedec(data_np, wavelet=wavelet, level=level)

    print(f"Wavelet decomposition completed for test data. Generated {len(coeffs)} components.")

    return coeffs

# Rolling window prediction functions
def create_rolling_sequences(data, lookback_len, pred_length):
    """
    Create rolling window sequences for prediction

    Parameters:
    - data: Time series data
    - lookback_len: Length of lookback window
    - pred_length: Length of prediction window

    Returns:
    - sequences: Input sequences for prediction
    - targets: Target values for evaluation
    """
    sequences = []
    targets = []

    for i in range(len(data) - lookback_len - pred_length + 1):
        sequence = data[i:i + lookback_len]
        target = data[i + lookback_len:i + lookback_len + pred_length]
        sequences.append(sequence)
        targets.append(target)

    return np.array(sequences), np.array(targets)

def predict_with_itransformer(model, sequences, scaler):
    """Make predictions using iTransformer model with proper normalization."""
    # Normalize sequences per saved scaler
    normalized_sequences = scaler.transform(sequences.reshape(-1, 1)).reshape(sequences.shape[0], sequences.shape[1])

    # Convert to tensor with variate dimension [batch, lookback_len, 1]
    sequences_tensor = torch.tensor(normalized_sequences, dtype=torch.float32)
    if sequences_tensor.ndim == 2:
        sequences_tensor = sequences_tensor.unsqueeze(-1)

    model.eval()
    with torch.no_grad():
        predictions = model(sequences_tensor)
        if isinstance(predictions, dict):
            first_key = list(predictions.keys())[0]
            predictions = predictions[first_key]

        # Extract predictions - shape [batch, pred_length, features] or [batch, pred_length]
        if len(predictions.shape) == 3:
            predictions = predictions[:, :, 0]

    predictions = predictions.numpy()

    # Inverse transform predictions back to original scale
    if predictions.ndim == 1:
        predictions = scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()
    else:
        pred_list = []
        for i in range(predictions.shape[1]):
            step_pred = scaler.inverse_transform(predictions[:, i].reshape(-1, 1)).flatten()
            pred_list.append(step_pred)
        predictions = np.array(pred_list).T

    return predictions

def predict_with_lstm(model, sequences, scaler):
    """Make predictions using LSTM model"""
    # Normalize sequences
    normalized_sequences = scaler.transform(sequences.reshape(-1, 1))
    normalized_sequences = normalized_sequences.reshape(sequences.shape[0], 1, sequences.shape[1])

    # Make predictions
    predictions = model.predict(normalized_sequences)

    # Inverse transform predictions
    if len(predictions.shape) == 1:
        predictions = predictions.reshape(-1, 1)
        predictions = scaler.inverse_transform(predictions).flatten()
    else:
        # For multi-step predictions, inverse transform each step
        pred_list = []
        for i in range(predictions.shape[1]):
            step_pred = predictions[:, i].reshape(-1, 1)
            step_pred = scaler.inverse_transform(step_pred).flatten()
            pred_list.append(step_pred)
        predictions = np.array(pred_list).T  # Shape: [samples, pred_length]

    return predictions

def rolling_window_validation_wavelet(models, scalers, config, test_wavelet_coeffs):
    """
    Perform rolling window validation with configurable prediction length for wavelet components

    Parameters:
    - models: Dictionary of trained models
    - scalers: Dictionary of scalers
    - config: Configuration dictionary
    - test_wavelet_coeffs: Test wavelet coefficients

    Returns:
    - final_predictions: Combined predictions from all components
    - actual_values: Actual target values
    """
    pred_length = config['pred_length']
    lookback_len = config['lookback_len']
    wavelet_name = config['wavelet_name']

    print(f"Performing rolling window validation with prediction length: {pred_length}")

    component_predictions = []

    for i, component_data in enumerate(test_wavelet_coeffs):
        print(f"Processing Component {i + 1}/{len(test_wavelet_coeffs)}...")

        # Reconstruct each component before prediction
        temp_coeffs_for_reconstruction = [np.zeros_like(c) for c in test_wavelet_coeffs]
        temp_coeffs_for_reconstruction[i] = component_data
        reconstructed_component = pywt.waverec(temp_coeffs_for_reconstruction, wavelet_name)

        # Create rolling sequences
        sequences, targets = create_rolling_sequences(
            reconstructed_component, lookback_len, pred_length
        )

        if len(sequences) == 0:
            print(f"Warning: No sequences generated for Component {i+1}")
            continue

        # Get model and scaler
        if i == 0:
            model_name = f'itransformer_component_{i+1}'
            model = models[model_name]
            scaler = scalers[f'scaler_component_{i+1}']

            # Make predictions with iTransformer
            predictions = predict_with_itransformer(model, sequences, scaler)
        else:
            model_name = f'lstm_component_{i+1}'
            model = models[model_name]
            scaler = scalers[f'scaler_component_{i+1}']

            # Make predictions with LSTM
            predictions = predict_with_lstm(model, sequences, scaler)

        component_predictions.append(predictions)
        print(f"Component {i+1} predictions shape: {predictions.shape}")

    # Combine predictions from all components
    if len(component_predictions) > 0:
        # Ensure all predictions have the same shape
        min_samples = min([pred.shape[0] for pred in component_predictions])

        final_predictions = np.zeros((min_samples, pred_length))
        for pred in component_predictions:
            if len(pred.shape) == 1:
                # Single step prediction, repeat for all steps
                final_predictions += pred[:min_samples].reshape(-1, 1)
            else:
                # Multi-step prediction
                final_predictions += pred[:min_samples, :]

        # Get actual values for evaluation (using the first component's targets as reference)
        temp_coeffs_for_reconstruction = [np.zeros_like(c) for c in test_wavelet_coeffs]
        temp_coeffs_for_reconstruction[0] = test_wavelet_coeffs[0]
        reconstructed_first_component = pywt.waverec(temp_coeffs_for_reconstruction, wavelet_name)

        _, actual_targets = create_rolling_sequences(
            reconstructed_first_component, lookback_len, pred_length
        )
        actual_values = actual_targets[:min_samples]

        return final_predictions, actual_values
    else:
        print("Error: No predictions generated")
        return None, None

def test_with_proper_wavelet_approach(models, scalers, proper_test_components, config):
    """
    Test using properly extracted wavelet test components from complete dataset decomposition

    Parameters:
    - models: Dictionary of trained models
    - scalers: Dictionary of scalers for each component
    - proper_test_components: Dictionary of properly extracted test components
    - config: Configuration dictionary

    Returns:
    - final_predictions: Combined predictions from all components
    - actual_values: Actual values for comparison
    """
    lookback_len = config['lookback_len']
    pred_length = config['pred_length']

    component_predictions = []
    component_actuals = []

    print(f"Testing with proper wavelet approach using correctly extracted test components (prediction length: {pred_length})")

    # Determine available component indices from loaded models
    available_indices = []
    if 'itransformerfft_component_0' in models:
        available_indices.append(0)
    for key in models.keys():
        if key.startswith('lstm_cnn_component_'):
            try:
                available_indices.append(int(key.split('_')[-1]))
            except Exception:
                pass
    available_indices = sorted(set(available_indices))

    for comp_idx in available_indices:
        print(f"\nProcessing Component {comp_idx+1}")

        # Get the properly extracted test data for this component (dict is 1-indexed)
        test_data = proper_test_components.get(f'test_component_{comp_idx+1}')
        if test_data is None:
            print(f"Warning: test data for component {comp_idx} not found; skipping")
            continue

        # Create sequences from test data
        sequences, targets = create_rolling_sequences(
            test_data.flatten(), lookback_len, pred_length
        )

        if len(sequences) == 0:
            print(f"Warning: No sequences generated for Component {comp_idx}")
            continue

        # Get model and scaler
        if comp_idx == 0:
            model = models['itransformerfft_component_0']
            scaler = scalers['itransformerfft_component_0']
            predictions = predict_with_itransformer(model, sequences, scaler)
        else:
            lstm_key = f'lstm_cnn_component_{comp_idx}'
            if lstm_key not in models or lstm_key not in scalers:
                print(f"Warning: Missing model or scaler for {lstm_key}; skipping")
                continue
            model = models[lstm_key]
            scaler = scalers[lstm_key]
            predictions = predict_with_lstm(model, sequences, scaler)

        # Store predictions and actuals
        component_predictions.append(predictions)

        # Ensure actuals are on original scale
        targets_scaled = scaler.transform(targets.reshape(-1, 1))
        actual_values = scaler.inverse_transform(targets_scaled).flatten()
        component_actuals.append(actual_values)

        print(f"Component {comp_idx+1} - Predictions shape: {predictions.shape}, Actuals shape: {actual_values.shape}")

    # Combine predictions from all components
    if component_predictions:
        # Ensure all predictions have the same length
        min_length = min(len(pred) for pred in component_predictions)
        final_predictions = np.zeros(min_length)
        final_actuals = np.zeros(min_length)

        for pred, actual in zip(component_predictions, component_actuals):
            # Handle different prediction shapes
            if len(pred.shape) == 2:
                pred_flat = pred[:min_length, 0]  # Take first column if 2D
            else:
                pred_flat = pred[:min_length]  # Already 1D

            if len(actual.shape) == 2:
                actual_flat = actual[:min_length, 0]  # Take first column if 2D
            else:
                actual_flat = actual[:min_length]  # Already 1D

            final_predictions += pred_flat
            final_actuals += actual_flat

        print(f"Final combined predictions shape: {final_predictions.shape}")
        return final_predictions, final_actuals
    else:
        print("Warning: No valid predictions generated")
        return np.array([]), np.array([])

def test_with_original_approach(models, scalers, test_data_components, config):
    """
    Test using the same test data that was used during training (original approach)
    This is kept for backward compatibility
    """
    return test_with_proper_wavelet_approach(models, scalers, test_data_components, config)

def evaluate_and_visualize_wavelet_results(predictions, actual_values, config):
    """
    Evaluate wavelet predictions and create visualizations

    Parameters:
    - predictions: Model predictions
    - actual_values: Actual target values
    - config: Configuration dictionary
    """
    pred_length = config['pred_length']

    print(f"\n=== Wavelet Evaluation Results ===")
    print(f"Predictions shape: {predictions.shape}")
    print(f"Actual values shape: {actual_values.shape}")

    # For multi-step predictions, evaluate each step and overall
    if len(predictions.shape) == 2 and predictions.shape[1] > 1:
        print(f"\nMulti-step prediction evaluation (prediction length: {pred_length}):")

        step_metrics = []
        for step in range(pred_length):
            pred_step = predictions[:, step]
            actual_step = actual_values[:, step]

            # Calculate metrics for this step
            mse = mean_squared_error(actual_step, pred_step)
            mae = mean_absolute_error(actual_step, pred_step)
            rmse = np.sqrt(mse)
            r2 = r2_score(actual_step, pred_step)
            mape_val = mape(actual_step, pred_step)
            trend_acc = calculate_trend_accuracy(actual_step, pred_step)

            step_metrics.append({
                'step': step + 1,
                'mse': mse,
                'mae': mae,
                'rmse': rmse,
                'r2': r2,
                'mape': mape_val,
                'trend_accuracy': trend_acc
            })

            print(f"Step {step + 1}: RMSE={rmse:.4f}, R2={r2:.4f}, MAPE={mape_val:.4f}, Trend Acc={trend_acc:.2f}%")

        # Overall evaluation using the last step
        final_predictions = predictions[:, -1]
        final_actual = actual_values[:, -1]
    else:
        # Single step prediction
        final_predictions = predictions.flatten() if len(predictions.shape) > 1 else predictions
        final_actual = actual_values.flatten() if len(actual_values.shape) > 1 else actual_values
        step_metrics = []

    # Calculate overall metrics
    mse = mean_squared_error(final_actual, final_predictions)
    mae = mean_absolute_error(final_actual, final_predictions)
    rmse = np.sqrt(mse)
    r2 = r2_score(final_actual, final_predictions)
    mape_val = mape(final_actual, final_predictions)
    trend_accuracy = calculate_trend_accuracy(final_actual, final_predictions)

    print(f"\nOverall Performance Metrics:")
    print(f"R2: {r2:.4f}")
    print(f"MAPE: {mape_val:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"MSE: {mse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"Trend Accuracy: {trend_accuracy:.2f}%")

    # Save prediction results to CSV
    if len(predictions.shape) == 2 and predictions.shape[1] > 1:
        # Multi-step predictions
        results_data = {'actual_final': final_actual, 'predicted_final': final_predictions}
        for step in range(pred_length):
            results_data[f'actual_step_{step+1}'] = actual_values[:, step]
            results_data[f'predicted_step_{step+1}'] = predictions[:, step]
        results_df = pd.DataFrame(results_data)
    else:
        # Single step predictions
        results_df = pd.DataFrame({
            'actual': final_actual,
            'predicted': final_predictions
        })

    results_df.to_csv('./figure/wavelet/wavelet_test_prediction_results.csv', index=False)
    print("Wavelet test prediction results saved to ./figure/wavelet/wavelet_test_prediction_results.csv")

    # Save detailed results
    detailed_results = {
        'R2': r2,
        'MAPE': mape_val,
        'RMSE': rmse,
        'MSE': mse,
        'MAE': mae,
        'Trend_Accuracy_Percent': trend_accuracy,
        'Prediction_Length': pred_length,
        'Total_Predictions': len(final_actual),
        'Wavelet': config['wavelet_name'],
        'Decomposition_Level': config['decomp_level']
    }

    # Add step-wise metrics if available
    if step_metrics:
        detailed_results['Step_Metrics'] = step_metrics

    with open('./figure/wavelet/wavelet_test_detailed_results.txt', 'w') as f:
        f.write("=== Wavelet-FFTTransformer-LSTM Test Results ===\n")
        for metric, value in detailed_results.items():
            if metric != 'Step_Metrics':
                f.write(f"{metric}: {value}\n")

        if step_metrics:
            f.write("\n=== Step-wise Metrics ===\n")
            for step_metric in step_metrics:
                f.write(f"Step {step_metric['step']}: ")
                f.write(f"RMSE={step_metric['rmse']:.4f}, ")
                f.write(f"R2={step_metric['r2']:.4f}, ")
                f.write(f"MAPE={step_metric['mape']:.4f}, ")
                f.write(f"Trend_Acc={step_metric['trend_accuracy']:.2f}%\n")

    print("Detailed wavelet test results saved to ./figure/wavelet/wavelet_test_detailed_results.txt")

    # Create and save prediction plot
    plt.figure(figsize=(18, 12))

    if len(predictions.shape) == 2 and predictions.shape[1] > 1:
        # Plot multi-step predictions (show final step)
        index = range(len(final_actual))
        plt.plot(index, final_predictions, label=f"Predicted (Step {pred_length})", linewidth=2, alpha=0.8)
        plt.plot(index, final_actual, label=f"Actual (Step {pred_length})", linewidth=2, alpha=0.8)
        title = f"Wavelet Test Results - {pred_length}-Step Prediction\nR2: {r2:.4f}, RMSE: {rmse:.4f}, Trend Accuracy: {trend_accuracy:.2f}%"
    else:
        # Plot single step predictions
        index = range(len(final_actual))
        plt.plot(index, final_predictions, label="Predicted", linewidth=2, alpha=0.8)
        plt.plot(index, final_actual, label="Actual", linewidth=2, alpha=0.8)
        title = f"Wavelet Test Results - Single Step Prediction\nR2: {r2:.4f}, RMSE: {rmse:.4f}, Trend Accuracy: {trend_accuracy:.2f}%"

    plt.xlabel('Time Steps')
    plt.ylabel('Price')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('./figure/wavelet/wavelet_test_prediction_results.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Wavelet test prediction plot saved to ./figure/wavelet/wavelet_test_prediction_results.png")

    return detailed_results

def run_wavelet_testing_pipeline():
    """
    Main wavelet testing pipeline that loads models, processes test data, and evaluates performance
    """
    print("=== Starting Wavelet Testing Pipeline ===")

    # Load trained models and configuration
    try:
        models, scalers, saved_test_data_components, config = load_wavelet_model_components(MODEL_LOAD_DIR)
        # Note: We load saved_test_data_components but don't use them because we need to
        # perform wavelet decomposition on the complete dataset for mathematical integrity
    except Exception as e:
        print(f"Error loading wavelet models: {e}")
        print("Please ensure that the wavelet training script has been run and models are saved.")
        return

    print(f"Loaded configuration: Prediction Length = {config['pred_length']}, Lookback Length = {config['lookback_len']}")
    print(f"Wavelet: {config['wavelet_name']}, Decomposition Level = {config['decomp_level']}")

    # Extract proper test components from complete dataset decomposition
    print("\n🔧 IMPORTANT: Performing wavelet decomposition on complete dataset for mathematical integrity...")
    print("This ensures test components are consistent with training components.")
    proper_test_components = load_complete_data_and_extract_test_components(config)

    # Test using the proper wavelet approach
    print("Testing with proper wavelet approach...")
    predictions, actual_values = test_with_proper_wavelet_approach(
        models, scalers, proper_test_components, config
    )

    if predictions is not None and actual_values is not None:
        # Evaluate and visualize results
        results = evaluate_and_visualize_wavelet_results(predictions, actual_values, config)

        print("\n=== Wavelet Testing Pipeline Completed Successfully ===")
        print(f"Test results saved to: ./figure/wavelet/")
        print(f"Configuration: Prediction Length = {config['pred_length']}, Lookback Length = {config['lookback_len']}")
        print(f"Wavelet: {config['wavelet_name']}, Decomposition Level = {config['decomp_level']}")
        print(f"Total test samples: {len(predictions)}")

        return results
    else:
        print("Error: Failed to generate predictions")
        return None

if __name__ == "__main__":
    try:
        results = run_wavelet_testing_pipeline()
        if results:
            print(f"\nFinal Wavelet Test Performance:")
            print(f"R2 Score: {results['R2']:.4f}")
            print(f"RMSE: {results['RMSE']:.4f}")
            print(f"Trend Accuracy: {results['Trend_Accuracy_Percent']:.2f}%")
    except Exception as e:
        print(f"Error during wavelet testing: {e}")
        import traceback
        traceback.print_exc()